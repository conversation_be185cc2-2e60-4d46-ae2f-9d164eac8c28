# Windows 硬件工具箱

一款功能全面的Windows硬件优化工具，提供硬件信息查看、系统优化、应用卸载、超频工具和快捷工具等功能。

## ✨ 主要功能

### 🔧 硬件信息
- **系统信息**: 操作系统版本、计算机名称、用户信息
- **CPU信息**: 处理器型号、核心数、频率、架构
- **内存信息**: 总容量、可用容量、使用率、内存条详情
- **GPU信息**: 显卡型号、显存、驱动版本
- **磁盘信息**: 硬盘型号、容量、分区信息
- **主板信息**: 主板型号、BIOS版本、芯片组

### ⚡ 系统优化
- **电源选项解锁**: 解锁Windows隐藏的高级电源选项
- **注册表优化**: 系统性能优化、界面响应速度提升
- **系统清理**: 清理临时文件、缓存、垃圾文件

### 🗑️ 应用管理
- **预装应用卸载**: 批量卸载Windows预装应用
- **OneDrive清理**: 完全移除OneDrive及相关组件

### 🚀 超频工具
- 集成多种专业超频和测试工具
- 一键启动各种硬件测试软件

### 🛠️ 快捷工具
- **系统管理**: 控制面板、设备管理器、磁盘管理等
- **网络工具**: 网络配置、连接诊断
- **诊断监控**: 性能监视器、事件查看器、系统信息
- **系统维护**: 命令提示符、PowerShell、安全模式
- **电源控制**: 重启、关机、睡眠、锁定

## 🚀 快速开始

### 环境要求
- Windows 10/11 (64位)
- Python 3.8+
- 管理员权限（部分功能需要）

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行程序
```bash
python main.py
```

## 📦 依赖包

- **PySide6**: Qt6 Python绑定，提供现代化GUI界面
- **PySide6-Fluent-Widgets**: Fluent Design风格的UI组件库
- **py-cpuinfo**: CPU信息获取
- **psutil**: 系统和进程信息
- **wmi**: Windows管理接口
- **pywin32**: Windows API调用

## 🏗️ 项目结构

```
Windows轻松优化工具/
├── app/                    # 应用程序主目录
│   ├── components/         # UI组件
│   │   ├── common.py      # 通用组件
│   │   ├── progress_dialog.py  # 进度对话框
│   │   └── dialogs/       # 对话框组件
│   ├── core/              # 核心功能模块
│   │   ├── app_manager.py # 应用管理器
│   │   ├── hardware_manager.py # 硬件信息管理
│   │   ├── command_runner.py   # 命令执行引擎
│   │   ├── permission_manager.py # 权限管理
│   │   ├── task_manager.py     # 任务管理
│   │   ├── exceptions.py       # 异常处理
│   │   └── dialog_manager.py   # 对话框管理
│   ├── utils/             # 工具模块
│   │   ├── async_task.py  # 异步任务
│   │   ├── splash_screen.py # 启动画面
│   │   └── icon_extractor.py # 图标提取
│   ├── views/             # 视图模块
│   │   ├── hardware_info_view.py # 硬件信息视图
│   │   ├── optimization_view.py  # 优化清理视图
│   │   ├── app_removal_view.py   # 应用卸载视图
│   │   ├── overclocking_view.py  # 超频工具视图
│   │   ├── quick_tools_view.py   # 快捷工具视图
│   │   └── about_view.py         # 关于软件视图
│   └── main_window.py     # 主窗口
├── config/                # 配置文件
│   ├── app_config.py      # 应用配置
│   ├── powershell_commands.py # PowerShell命令
│   ├── registry_commands.py   # 注册表命令
│   ├── system_cleanup.py      # 系统清理配置
│   ├── appx_packages.py       # 应用包配置
│   ├── onedrive_cleanup.py    # OneDrive清理配置
│   └── quick_tools.py         # 快捷工具配置
├── assets/                # 资源文件
│   ├── icon.ico          # 应用图标
│   ├── alipay.png        # 支付宝二维码
│   └── wechat_pay.png    # 微信支付二维码
├── OCTools/              # 超频工具集合
├── docs/                 # 文档
└── main.py              # 程序入口
```

## 🔧 核心特性

### 异步处理
- 使用异步任务处理耗时操作，避免界面卡顿
- 支持任务取消和进度跟踪
- 线程安全的资源管理

### 权限管理
- 智能检测管理员权限
- UAC权限提升支持
- 安全的权限验证机制

### 错误处理
- 统一的异常处理框架
- 分层错误处理和重试机制
- 用户友好的错误提示

### 懒加载
- 视图懒加载，提升启动速度
- 按需加载资源，优化内存使用

## ⚠️ 注意事项

1. **管理员权限**: 系统优化、注册表修改等功能需要管理员权限
2. **系统兼容性**: 主要针对Windows 10/11设计，其他版本可能存在兼容性问题
3. **风险提示**: 注册表修改和系统清理操作具有一定风险，请谨慎使用
4. **备份建议**: 执行系统修改前建议创建系统还原点

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

### 开发环境设置
1. Fork本项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 提交Pull Request

### 代码规范
- 遵循PEP 8代码风格
- 添加适当的类型注解
- 编写清晰的文档字符串
- 确保代码通过所有测试

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [PySide6](https://doc.qt.io/qtforpython/) - 强大的Python GUI框架
- [PySide6-Fluent-Widgets](https://qfluentwidgets.com/) - 美观的Fluent Design组件库
- [psutil](https://github.com/giampaolo/psutil) - 系统信息获取库

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交Issue: [GitHub Issues](https://github.com/yourusername/WindowsHardwareToolbox/issues)
- 邮箱: <EMAIL>

---

**免责声明**: 本软件仅供学习和研究使用，使用者需自行承担使用风险。作者不对因使用本软件造成的任何损失负责。
