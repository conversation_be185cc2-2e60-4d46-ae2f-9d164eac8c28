#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
进度对话框组件
提供任务执行进度的可视化显示和取消功能
"""

from typing import Optional, List
import time

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QProgressBar, QTextEdit, QFrame
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont
from qfluentwidgets import (
    PrimaryPushButton, PushButton, ProgressBar, 
    SubtitleLabel, BodyLabel, CardWidget
)

from app.core.task_manager import TaskManager, TaskProgress, TaskResult, TaskStatus


class TaskProgressWidget(CardWidget):
    """单个任务进度显示组件"""
    
    def __init__(self, task_id: str, task_name: str, parent=None):
        super().__init__(parent)
        self.task_id = task_id
        self.task_name = task_name
        self._init_ui()
        
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(8)
        
        # 任务名称
        self.name_label = BodyLabel(self.task_name)
        self.name_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(self.name_label)
        
        # 进度条
        self.progress_bar = ProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)
        
        # 状态信息
        info_layout = QHBoxLayout()
        self.status_label = BodyLabel("等待中...")
        self.percentage_label = BodyLabel("0%")
        
        info_layout.addWidget(self.status_label)
        info_layout.addStretch()
        info_layout.addWidget(self.percentage_label)
        
        layout.addLayout(info_layout)
        
    def update_progress(self, progress: TaskProgress):
        """更新进度"""
        if progress.task_id == self.task_id:
            self.progress_bar.setValue(int(progress.percentage))
            self.percentage_label.setText(f"{progress.percentage:.1f}%")
            
            if progress.message:
                self.status_label.setText(progress.message)
            else:
                self.status_label.setText(f"{progress.current}/{progress.total}")
                
    def update_status(self, status: TaskStatus):
        """更新状态"""
        status_text = {
            TaskStatus.PENDING: "等待中...",
            TaskStatus.RUNNING: "执行中...",
            TaskStatus.COMPLETED: "已完成",
            TaskStatus.FAILED: "执行失败",
            TaskStatus.CANCELLED: "已取消"
        }
        
        self.status_label.setText(status_text.get(status, "未知状态"))
        
        # 根据状态设置进度条颜色
        if status == TaskStatus.COMPLETED:
            self.progress_bar.setValue(100)
            self.percentage_label.setText("100%")
        elif status == TaskStatus.FAILED:
            self.progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #e74c3c; }")
        elif status == TaskStatus.CANCELLED:
            self.progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #95a5a6; }")


class ProgressDialog(QDialog):
    """进度对话框
    
    显示多个任务的执行进度，支持取消操作
    """
    
    # 信号定义
    cancelled = Signal()  # 用户取消信号
    
    def __init__(self, title: str = "任务执行中", parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(500, 400)
        
        # 任务管理器
        self.task_manager: Optional[TaskManager] = None
        self.task_widgets: dict = {}
        self.task_ids: List[str] = []
        
        # 时间跟踪
        self.start_time = time.time()
        
        self._init_ui()
        self._setup_timer()
        
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 标题
        self.title_label = SubtitleLabel("正在执行任务...")
        layout.addWidget(self.title_label)
        
        # 总体进度
        progress_layout = QVBoxLayout()
        
        self.overall_label = BodyLabel("总体进度:")
        progress_layout.addWidget(self.overall_label)
        
        self.overall_progress = ProgressBar()
        self.overall_progress.setRange(0, 100)
        self.overall_progress.setValue(0)
        progress_layout.addWidget(self.overall_progress)
        
        # 进度信息
        info_layout = QHBoxLayout()
        self.time_label = BodyLabel("已用时间: 00:00")
        self.task_count_label = BodyLabel("任务: 0/0")
        
        info_layout.addWidget(self.time_label)
        info_layout.addStretch()
        info_layout.addWidget(self.task_count_label)
        
        progress_layout.addLayout(info_layout)
        layout.addLayout(progress_layout)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(separator)
        
        # 任务列表
        self.tasks_layout = QVBoxLayout()
        layout.addLayout(self.tasks_layout)
        
        # 添加弹性空间
        layout.addStretch()
        
        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_button = PushButton("取消")
        self.cancel_button.clicked.connect(self._on_cancel_clicked)
        button_layout.addWidget(self.cancel_button)
        
        self.close_button = PrimaryPushButton("关闭")
        self.close_button.clicked.connect(self.accept)
        self.close_button.setEnabled(False)
        button_layout.addWidget(self.close_button)
        
        layout.addLayout(button_layout)
        
    def _setup_timer(self):
        """设置定时器"""
        self.timer = QTimer(self)
        self.timer.timeout.connect(self._update_time)
        self.timer.start(1000)  # 每秒更新一次
        
    def set_task_manager(self, task_manager: TaskManager):
        """设置任务管理器"""
        self.task_manager = task_manager
        
        # 连接信号
        task_manager.taskStarted.connect(self._on_task_started)
        task_manager.taskProgress.connect(self._on_task_progress)
        task_manager.taskCompleted.connect(self._on_task_completed)
        task_manager.allTasksCompleted.connect(self._on_all_tasks_completed)
        
    def add_task(self, task_id: str, task_name: str):
        """添加任务"""
        if task_id not in self.task_widgets:
            widget = TaskProgressWidget(task_id, task_name)
            self.task_widgets[task_id] = widget
            self.tasks_layout.addWidget(widget)
            self.task_ids.append(task_id)
            
            self._update_task_count()
            
    def _on_task_started(self, task_id: str, task_name: str):
        """任务开始处理"""
        self.add_task(task_id, task_name)
        
    def _on_task_progress(self, progress: TaskProgress):
        """任务进度更新处理"""
        if progress.task_id in self.task_widgets:
            self.task_widgets[progress.task_id].update_progress(progress)
            
        self._update_overall_progress()
        
    def _on_task_completed(self, result: TaskResult):
        """任务完成处理"""
        if result.task_id in self.task_widgets:
            self.task_widgets[result.task_id].update_status(result.status)
            
        self._update_overall_progress()
        self._update_task_count()
        
    def _on_all_tasks_completed(self, results: List[TaskResult]):
        """所有任务完成处理"""
        self.title_label.setText("任务执行完成")
        self.cancel_button.setEnabled(False)
        self.close_button.setEnabled(True)
        self.timer.stop()
        
        # 统计结果
        completed = sum(1 for r in results if r.status == TaskStatus.COMPLETED)
        failed = sum(1 for r in results if r.status == TaskStatus.FAILED)
        cancelled = sum(1 for r in results if r.status == TaskStatus.CANCELLED)
        
        if failed > 0 or cancelled > 0:
            self.overall_label.setText(f"完成: {completed}, 失败: {failed}, 取消: {cancelled}")
        else:
            self.overall_label.setText(f"所有 {completed} 个任务执行成功")
            
    def _update_overall_progress(self):
        """更新总体进度"""
        if not self.task_ids:
            return
            
        total_tasks = len(self.task_ids)
        completed_tasks = 0
        
        for task_id in self.task_ids:
            if self.task_manager:
                status = self.task_manager.get_task_status(task_id)
                if status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    completed_tasks += 1
                    
        progress = (completed_tasks / total_tasks) * 100 if total_tasks > 0 else 0
        self.overall_progress.setValue(int(progress))
        
    def _update_task_count(self):
        """更新任务计数"""
        total = len(self.task_ids)
        completed = 0
        
        if self.task_manager:
            for task_id in self.task_ids:
                status = self.task_manager.get_task_status(task_id)
                if status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    completed += 1
                    
        self.task_count_label.setText(f"任务: {completed}/{total}")
        
    def _update_time(self):
        """更新时间显示"""
        elapsed = int(time.time() - self.start_time)
        minutes = elapsed // 60
        seconds = elapsed % 60
        self.time_label.setText(f"已用时间: {minutes:02d}:{seconds:02d}")
        
    def _on_cancel_clicked(self):
        """取消按钮点击处理"""
        if self.task_manager:
            self.task_manager.cancel_all_tasks()
        self.cancelled.emit()
        
    def closeEvent(self, event):
        """关闭事件处理"""
        if self.task_manager and self.task_manager.get_running_tasks():
            # 如果还有任务在运行，询问是否取消
            from PySide6.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self, "确认关闭", 
                "还有任务正在执行，是否取消所有任务并关闭？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.task_manager.cancel_all_tasks()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
