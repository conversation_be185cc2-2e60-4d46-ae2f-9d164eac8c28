#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
硬件信息管理
提供硬件信息获取和处理功能
"""

import platform
import time
import traceback
from typing import Dict, Any, Optional, Tuple
import wmi
import psutil
import datetime
import pythoncom  # 添加pythoncom导入
from PySide6.QtCore import QObject, Signal

# 直接导入async_task函数和类
from app.utils.async_task import async_task, AsyncTask, ThreadPoolTask


class HardwareManager(QObject):
    """硬件信息管理器"""

    # 信号定义
    hardwareInfoReady = Signal(dict)  # 硬件信息准备完成，参数: 格式化后的硬件信息
    hardwareInfoError = Signal(str)  # 硬件信息获取失败

    def __init__(self, parent=None):
        super().__init__(parent)
        self._hardware_info = {}
        # 移除WMI客户端初始化
        # self._wmi_client = None
        # self._init_wmi()

    def _get_wmi_client(self):
        """获取WMI客户端(线程安全方式)

        在每个需要WMI的线程中都要初始化COM

        Returns:
            wmi.WMI: WMI客户端实例，如果失败则返回None
        """
        try:
            if platform.system() == "Windows":
                # 初始化当前线程的COM环境
                pythoncom.CoInitialize()
                # 返回新的WMI客户端实例
                return wmi.WMI()
            return None
        except Exception as e:
            print(f"初始化WMI客户端失败: {e}")
            # 确保在发生错误时正确释放COM环境
            pythoncom.CoUninitialize()
            return None

    def _wmi_date_to_str(self, wmi_date: Optional[str]) -> Optional[str]:
        """转换WMI日期格式为ISO 8601格式"""
        if wmi_date is None:
            return None
        try:
            # 格式为: yyyymmddhhmmss.ffffff+/-zzz
            dt = datetime.datetime.strptime(wmi_date.split(".")[0], "%Y%m%d%H%M%S")
            return dt.isoformat()
        except (ValueError, IndexError):
            return str(wmi_date)

    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        wmi_client = self._get_wmi_client()
        if wmi_client is None:
            return {"error": "WMI客户端未初始化"}

        try:
            os_info = wmi_client.Win32_OperatingSystem()[0]
            result = {
                "caption": os_info.Caption,
                "version": os_info.Version,
                "os_architecture": os_info.OSArchitecture,
                "hostname": os_info.CSName,
            }
            # 清理COM环境
            pythoncom.CoUninitialize()
            return result
        except Exception as e:
            pythoncom.CoUninitialize()  # 确保在发生异常时也释放COM
            return {"error": str(e), "traceback": traceback.format_exc()}

    def get_cpu_info(self) -> Dict[str, Any]:
        """获取CPU信息"""
        wmi_client = self._get_wmi_client()
        if wmi_client is None:
            return {"error": "WMI客户端未初始化"}

        try:
            processors = []
            for p in wmi_client.Win32_Processor():
                processors.append(
                    {
                        "device_id": p.DeviceID,
                        "name": p.Name,
                        "manufacturer": p.Manufacturer,
                        "current_clock_speed": f"{p.CurrentClockSpeed} MHz",
                        "max_clock_speed": f"{p.MaxClockSpeed} MHz",
                    }
                )

            result = {
                "wmi_processor_info": processors,
            }
            # 清理COM环境
            pythoncom.CoUninitialize()
            return result
        except Exception as e:
            pythoncom.CoUninitialize()
            return {"error": str(e), "traceback": traceback.format_exc()}

    def get_memory_info(self) -> Dict[str, Any]:
        """获取内存信息"""
        wmi_client = self._get_wmi_client()
        if wmi_client is None:
            return {"error": "WMI客户端未初始化"}

        try:
            physical_memory = []

            for mem in wmi_client.Win32_PhysicalMemory():
                physical_memory.append(
                    {
                        "capacity": f"{int(mem.Capacity) / (1024**3):.2f} GB",
                        "part_number": mem.PartNumber.strip(),
                        "speed": f"{mem.Speed} MHz",
                    }
                )

            result = {
                "physical_slots": physical_memory,
            }
            # 清理COM环境
            pythoncom.CoUninitialize()
            return result
        except Exception as e:
            pythoncom.CoUninitialize()
            return {"error": str(e), "traceback": traceback.format_exc()}

    def get_disk_info(self) -> Dict[str, Any]:
        """获取磁盘信息"""
        wmi_client = self._get_wmi_client()
        if wmi_client is None:
            return {"error": "WMI客户端未初始化"}

        try:
            physical_disks = []

            for disk in wmi_client.Win32_DiskDrive():
                physical_disks.append(
                    {
                        "model": disk.Model,
                        "size": f"{int(disk.Size) / (1024**3):.2f} GB",
                    }
                )

            result = {
                "physical_disks": physical_disks,
            }
            # 清理COM环境
            pythoncom.CoUninitialize()
            return result
        except Exception as e:
            pythoncom.CoUninitialize()
            return {"error": str(e), "traceback": traceback.format_exc()}

    def get_gpu_info(self) -> Dict[str, Any]:
        """获取GPU信息"""
        wmi_client = self._get_wmi_client()
        if wmi_client is None:
            return {"error": "WMI客户端未初始化"}

        try:
            gpus = []

            for gpu in wmi_client.Win32_VideoController():
                pnp_id = gpu.PNPDeviceID
                if pnp_id and "PCI" in pnp_id:
                    gpus.append(
                        {
                            "name": gpu.Name,
                            "pnp_device_id": pnp_id,
                            "driver_version": gpu.DriverVersion,
                            "current_refresh_rate": gpu.CurrentRefreshRate,
                            "current_horizontal_resolution": gpu.CurrentHorizontalResolution,
                            "current_vertical_resolution": gpu.CurrentVerticalResolution,
                        }
                    )

            result = {"gpus": gpus}
            # 清理COM环境
            pythoncom.CoUninitialize()
            return result
        except Exception as e:
            pythoncom.CoUninitialize()
            return {"error": str(e), "traceback": traceback.format_exc()}

    def get_motherboard_info(self) -> Dict[str, Any]:
        """获取主板信息"""
        wmi_client = self._get_wmi_client()
        if wmi_client is None:
            return {"error": "WMI客户端未初始化"}

        try:
            baseboard = wmi_client.Win32_BaseBoard()[0]
            bios = wmi_client.Win32_BIOS()[0]

            result = {
                "motherboard": {
                    "manufacturer": baseboard.Manufacturer,
                    "product": baseboard.Product,
                },
                "bios": {
                    "manufacturer": bios.Manufacturer,
                    "version": bios.SMBIOSBIOSVersion,
                    "release_date": self._wmi_date_to_str(bios.ReleaseDate),
                },
            }
            # 清理COM环境
            pythoncom.CoUninitialize()
            return result
        except Exception as e:
            pythoncom.CoUninitialize()
            return {"error": str(e), "traceback": traceback.format_exc()}

    def get_uptime(self) -> str:
        """获取系统运行时间"""
        try:
            uptime_seconds = time.time() - psutil.boot_time()
            days, remainder = divmod(int(uptime_seconds), 86400)
            hours, remainder = divmod(remainder, 3600)
            minutes, seconds = divmod(remainder, 60)
            return f"{days}天 {hours}小时 {minutes}分钟"
        except Exception as e:
            return f"获取运行时间失败: {str(e)}"

    def get_formatted_system_info(self, system_info: Dict[str, Any]) -> str:
        """格式化系统信息为可显示字符串

        Args:
            system_info: 原始系统信息字典

        Returns:
            str: 格式化后的系统信息字符串
        """
        if "error" in system_info:
            return f"获取系统信息失败: {system_info['error']}"

        system_text = f"{system_info.get('caption', 'Unknown')}\n"
        system_text += f"版本: {system_info.get('version', 'Unknown')}\n"
        system_text += f"架构: {system_info.get('os_architecture', 'Unknown')}\n"
        system_text += f"主机名: {system_info.get('hostname', 'Unknown')}"
        return system_text

    def get_formatted_cpu_info(self, cpu_info: Dict[str, Any]) -> str:
        """格式化CPU信息为可显示字符串

        Args:
            cpu_info: 原始CPU信息字典

        Returns:
            str: 格式化后的CPU信息字符串
        """
        if "error" in cpu_info:
            return f"获取CPU信息失败: {cpu_info['error']}"

        if "wmi_processor_info" not in cpu_info or not cpu_info["wmi_processor_info"]:
            return "未检测到CPU信息"

        processor = cpu_info["wmi_processor_info"][0]
        cpu_text = f"CPU型号: {processor.get('name', 'Unknown')}\n"
        cpu_text += f"制造商: {processor.get('manufacturer', 'Unknown')}\n"
        cpu_text += f"最大频率: {processor.get('max_clock_speed', 'Unknown')}"
        return cpu_text

    def get_formatted_motherboard_info(self, mb_info: Dict[str, Any]) -> str:
        """格式化主板信息为可显示字符串

        Args:
            mb_info: 原始主板信息字典

        Returns:
            str: 格式化后的主板信息字符串
        """
        if "error" in mb_info:
            return f"获取主板信息失败: {mb_info['error']}"

        if "motherboard" not in mb_info:
            return "未检测到主板信息"

        motherboard = mb_info["motherboard"]
        bios_info = mb_info.get("bios", {})

        mb_text = f"主板厂商: {motherboard.get('manufacturer', 'Unknown')}\n"
        mb_text = f"主板型号: {motherboard.get('product', 'Unknown')}\n"
        mb_text += f"BIOS版本: {bios_info.get('version', 'Unknown')}\n"
        mb_text += f"BIOS厂商: {bios_info.get('manufacturer', 'Unknown')}\n"

        release_date = bios_info.get("release_date", "")
        if release_date and "T" in release_date:
            release_date = release_date.split("T")[0]
        mb_text += f"发布日期: {release_date or 'Unknown'}"

        return mb_text

    def get_formatted_memory_info(self, memory_info: Dict[str, Any]) -> str:
        """格式化内存信息为可显示字符串

        Args:
            memory_info: 原始内存信息字典

        Returns:
            str: 格式化后的内存信息字符串
        """
        if "error" in memory_info:
            return f"获取内存信息失败: {memory_info['error']}"

        if "physical_slots" not in memory_info or not memory_info["physical_slots"]:
            return "未检测到内存信息"

        memory_slots = memory_info["physical_slots"]
        mem_text = ""
        total_memory = 0

        for i, slot in enumerate(memory_slots):
            mem_text += f"内存条 {i+1}: {slot.get('capacity', 'Unknown')} {slot.get('part_number', '')} {slot.get('speed', '')}\n"
            try:
                capacity = slot.get("capacity", "0 GB")
                if isinstance(capacity, str) and "GB" in capacity:
                    total_memory += float(capacity.split()[0])
            except:
                pass

        if total_memory > 0:
            mem_text += f"总内存: {total_memory} GB"

        return mem_text

    def get_formatted_gpu_info(self, gpu_info: Dict[str, Any]) -> str:
        """格式化GPU信息为可显示字符串

        Args:
            gpu_info: 原始GPU信息字典

        Returns:
            str: 格式化后的GPU信息字符串
        """
        if "error" in gpu_info:
            return f"获取GPU信息失败: {gpu_info['error']}"

        if "gpus" not in gpu_info or not gpu_info["gpus"]:
            return "未检测到GPU信息"

        gpu_text = ""
        for i, gpu in enumerate(gpu_info["gpus"]):
            gpu_text += f"显卡 {i+1}: {gpu.get('name', 'Unknown')}\n"
            gpu_text += f"驱动版本: {gpu.get('driver_version', 'Unknown')}\n"
            if (
                "current_horizontal_resolution" in gpu
                and "current_vertical_resolution" in gpu
            ):
                gpu_text += f"分辨率: {gpu.get('current_horizontal_resolution', '?')} x {gpu.get('current_vertical_resolution', '?')}\n"
            if "current_refresh_rate" in gpu:
                gpu_text += f"刷新率: {gpu.get('current_refresh_rate', '?')} Hz\n"

        return gpu_text.rstrip()

    def get_formatted_disk_info(self, disk_info: Dict[str, Any]) -> str:
        """格式化磁盘信息为可显示字符串

        Args:
            disk_info: 原始磁盘信息字典

        Returns:
            str: 格式化后的磁盘信息字符串
        """
        if "error" in disk_info:
            return f"获取磁盘信息失败: {disk_info['error']}"

        if "physical_disks" not in disk_info or not disk_info["physical_disks"]:
            return "未检测到磁盘信息"

        disk_text = ""
        for i, disk in enumerate(disk_info["physical_disks"]):
            disk_text += f"磁盘 {i+1}: {disk.get('model', 'Unknown')} ({disk.get('size', 'Unknown')})\n"

        return disk_text.rstrip()

    def _collect_all_hardware_info(self):
        """收集所有硬件信息(在单独的线程中调用此方法)

        注意：此方法应当在一个单独的线程中调用，以避免阻塞主线程

        Returns:
            dict: 包含原始数据和格式化数据的硬件信息字典
        """
        try:
            # 初始化COM
            pythoncom.CoInitialize()

            # 收集原始硬件信息
            raw_hardware_info = {
                "system": self.get_system_info(),
                "cpu": self.get_cpu_info(),
                "memory": self.get_memory_info(),
                "disk": self.get_disk_info(),
                "gpu": self.get_gpu_info(),
                "motherboard": self.get_motherboard_info(),
            }

            # 保存原始数据
            self._hardware_info = raw_hardware_info

            # 创建格式化后的硬件信息字典
            formatted_hardware_info = {
                "raw_data": raw_hardware_info,  # 保留原始数据供需要时使用
                "formatted": {
                    "system": self.get_formatted_system_info(
                        raw_hardware_info["system"]
                    ),
                    "cpu": self.get_formatted_cpu_info(raw_hardware_info["cpu"]),
                    "motherboard": self.get_formatted_motherboard_info(
                        raw_hardware_info["motherboard"]
                    ),
                    "memory": self.get_formatted_memory_info(
                        raw_hardware_info["memory"]
                    ),
                    "gpu": self.get_formatted_gpu_info(raw_hardware_info["gpu"]),
                    "disk": self.get_formatted_disk_info(raw_hardware_info["disk"]),
                },
            }

            return formatted_hardware_info
        except Exception as e:
            error_msg = f"获取硬件信息失败: {str(e)}"
            print(error_msg)
            print(traceback.format_exc())
            return {"error": error_msg}
        finally:
            # 确保COM环境被释放
            try:
                pythoncom.CoUninitialize()
            except:
                pass

    def fetch_hardware_info(self) -> None:
        """异步获取所有硬件信息"""

        def task_callback(result):
            """处理异步任务的结果"""
            if "error" in result:
                self.hardwareInfoError.emit(result["error"])
            else:
                self.hardwareInfoReady.emit(result)

        # 使用ThreadPoolTask执行硬件信息收集
        task = ThreadPoolTask(self._collect_all_hardware_info)
        task.taskFinished.connect(task_callback)
        task.taskError.connect(lambda error_msg: self.hardwareInfoError.emit(error_msg))
        task.run()

    def fetch_hardware_info_sync(self) -> Dict[str, Any]:
        """同步获取所有硬件信息

        Returns:
            Dict[str, Any]: 硬件信息字典
        """
        # 直接在当前线程中调用收集方法
        # 警告：这会阻塞当前线程，请小心使用
        result = self._collect_all_hardware_info()

        # 发送信号
        if "error" in result:
            self.hardwareInfoError.emit(result["error"])
        else:
            self.hardwareInfoReady.emit(result)

        return result

    def get_hardware_info(self) -> Dict[str, Any]:
        """获取硬件信息"""
        return self._hardware_info


# 单例模式
_hardware_manager_instance = None


def get_hardware_manager() -> HardwareManager:
    """获取HardwareManager实例(单例模式)

    Returns:
        HardwareManager: 硬件管理器实例
    """
    global _hardware_manager_instance
    if _hardware_manager_instance is None:
        _hardware_manager_instance = HardwareManager()
    return _hardware_manager_instance
