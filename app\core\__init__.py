#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
核心功能模块
"""

from app.core.command_runner import (
    CommandRunner,
    CommandResult,
    CommandType,
    CommandWorker,
    get_command_runner,
    is_gui_app,
)

from app.core.dialog_manager import DialogManager

from app.core.app_manager import A<PERSON><PERSON>anager, get_app_manager

from app.core.hardware_manager import HardwareManager, get_hardware_manager

from app.core.permission_manager import (
    PermissionManager,
    PermissionLevel,
    get_permission_manager,
    require_admin,
)

from app.core.task_manager import (
    TaskManager,
    TaskStatus,
    TaskProgress,
    TaskResult,
    CancellableTask,
    get_task_manager,
)

from app.core.exceptions import (
    BaseAppException,
    HardwareException,
    CommandExecutionException,
    PermissionException,
    ResourceException,
    ConfigurationException,
    TaskExecutionError,
    ErrorHandler,
    get_error_handler,
    handle_exceptions,
    safe_execute,
    retry_on_exception,
)

__all__ = [
    "CommandRunner",
    "CommandResult",
    "CommandType",
    "CommandWorker",
    "get_command_runner",
    "is_gui_app",
    "DialogManager",
    "AppManager",
    "get_app_manager",
    "HardwareManager",
    "get_hardware_manager",
    "PermissionManager",
    "PermissionLevel",
    "get_permission_manager",
    "require_admin",
    "TaskManager",
    "TaskStatus",
    "TaskProgress",
    "TaskResult",
    "CancellableTask",
    "get_task_manager",
    "BaseAppException",
    "HardwareException",
    "CommandExecutionException",
    "PermissionException",
    "ResourceException",
    "ConfigurationException",
    "TaskExecutionError",
    "ErrorHandler",
    "get_error_handler",
    "handle_exceptions",
    "safe_execute",
    "retry_on_exception",
]
