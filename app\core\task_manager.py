#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
任务管理器
提供可取消的异步任务执行和详细进度跟踪
"""

import uuid
import threading
import time
from typing import Dict, List, Optional, Callable, Any
from enum import Enum
from dataclasses import dataclass

from PySide6.QtCore import QObject, Signal, QThread, QTimer

from app.core.exceptions import handle_exceptions, TaskExecutionError


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"       # 等待执行
    RUNNING = "running"       # 正在执行
    COMPLETED = "completed"   # 已完成
    FAILED = "failed"         # 执行失败
    CANCELLED = "cancelled"   # 已取消


@dataclass
class TaskProgress:
    """任务进度信息"""
    task_id: str
    current: int
    total: int
    message: str
    percentage: float = 0.0
    
    def __post_init__(self):
        if self.total > 0:
            self.percentage = (self.current / self.total) * 100


@dataclass
class TaskResult:
    """任务结果"""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[Exception] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    
    @property
    def duration(self) -> Optional[float]:
        """获取任务执行时长"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


class CancellableTask(QThread):
    """可取消的任务线程"""
    
    # 信号定义
    progressUpdated = Signal(TaskProgress)  # 进度更新
    statusChanged = Signal(str, TaskStatus)  # 状态变更
    taskCompleted = Signal(TaskResult)       # 任务完成
    
    def __init__(self, task_id: str, func: Callable, *args, **kwargs):
        super().__init__()
        self.task_id = task_id
        self.func = func
        self.args = args
        self.kwargs = kwargs
        self._cancelled = False
        self._result = None
        self._error = None
        self.start_time = None
        self.end_time = None
        
    def cancel(self):
        """取消任务"""
        self._cancelled = True
        self.statusChanged.emit(self.task_id, TaskStatus.CANCELLED)
        
    def is_cancelled(self) -> bool:
        """检查任务是否被取消"""
        return self._cancelled
        
    def update_progress(self, current: int, total: int, message: str = ""):
        """更新进度"""
        if not self._cancelled:
            progress = TaskProgress(self.task_id, current, total, message)
            self.progressUpdated.emit(progress)
            
    def run(self):
        """执行任务"""
        if self._cancelled:
            return
            
        self.start_time = time.time()
        self.statusChanged.emit(self.task_id, TaskStatus.RUNNING)
        
        try:
            # 将进度更新函数传递给任务函数
            if 'progress_callback' in self.kwargs:
                self.kwargs['progress_callback'] = self.update_progress
            elif len(self.args) == 0:
                self.kwargs['progress_callback'] = self.update_progress
                
            self._result = self.func(*self.args, **self.kwargs)
            
            if not self._cancelled:
                self.statusChanged.emit(self.task_id, TaskStatus.COMPLETED)
                
        except Exception as e:
            self._error = e
            if not self._cancelled:
                self.statusChanged.emit(self.task_id, TaskStatus.FAILED)
                
        finally:
            self.end_time = time.time()
            
            # 发送完成信号
            result = TaskResult(
                task_id=self.task_id,
                status=TaskStatus.CANCELLED if self._cancelled else 
                       (TaskStatus.FAILED if self._error else TaskStatus.COMPLETED),
                result=self._result,
                error=self._error,
                start_time=self.start_time,
                end_time=self.end_time
            )
            self.taskCompleted.emit(result)


class TaskManager(QObject):
    """任务管理器
    
    管理多个异步任务的执行、取消和进度跟踪
    """
    
    # 信号定义
    taskStarted = Signal(str, str)           # 任务开始 (task_id, task_name)
    taskProgress = Signal(TaskProgress)       # 任务进度更新
    taskCompleted = Signal(TaskResult)        # 任务完成
    allTasksCompleted = Signal(list)          # 所有任务完成
    
    def __init__(self):
        super().__init__()
        self._tasks: Dict[str, CancellableTask] = {}
        self._task_names: Dict[str, str] = {}
        self._completed_tasks: List[TaskResult] = []
        self._lock = threading.Lock()
        
    @handle_exceptions("任务提交")
    def submit_task(self, func: Callable, task_name: str = "", *args, **kwargs) -> str:
        """提交任务
        
        Args:
            func: 要执行的函数
            task_name: 任务名称
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())
        task_name = task_name or f"Task-{task_id[:8]}"
        
        with self._lock:
            # 创建任务
            task = CancellableTask(task_id, func, *args, **kwargs)
            
            # 连接信号
            task.progressUpdated.connect(self.taskProgress.emit)
            task.statusChanged.connect(self._on_task_status_changed)
            task.taskCompleted.connect(self._on_task_completed)
            
            # 保存任务
            self._tasks[task_id] = task
            self._task_names[task_id] = task_name
            
            # 启动任务
            task.start()
            self.taskStarted.emit(task_id, task_name)
            
        return task_id
        
    def cancel_task(self, task_id: str) -> bool:
        """取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        with self._lock:
            if task_id in self._tasks:
                self._tasks[task_id].cancel()
                return True
        return False
        
    def cancel_all_tasks(self):
        """取消所有任务"""
        with self._lock:
            for task in self._tasks.values():
                task.cancel()
                
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[TaskStatus]: 任务状态，如果任务不存在则返回None
        """
        with self._lock:
            if task_id in self._tasks:
                task = self._tasks[task_id]
                if task.isRunning():
                    return TaskStatus.RUNNING
                elif task.isFinished():
                    return TaskStatus.COMPLETED if not task._error else TaskStatus.FAILED
                else:
                    return TaskStatus.PENDING
        return None
        
    def get_running_tasks(self) -> List[str]:
        """获取正在运行的任务ID列表
        
        Returns:
            List[str]: 正在运行的任务ID列表
        """
        with self._lock:
            return [task_id for task_id, task in self._tasks.items() if task.isRunning()]
            
    def get_completed_tasks(self) -> List[TaskResult]:
        """获取已完成的任务结果
        
        Returns:
            List[TaskResult]: 已完成的任务结果列表
        """
        return self._completed_tasks.copy()
        
    def clear_completed_tasks(self):
        """清除已完成的任务记录"""
        self._completed_tasks.clear()
        
    def _on_task_status_changed(self, task_id: str, status: TaskStatus):
        """任务状态变更处理"""
        pass  # 可以在这里添加状态变更的处理逻辑
        
    def _on_task_completed(self, result: TaskResult):
        """任务完成处理"""
        task_id = result.task_id
        
        with self._lock:
            # 移除任务引用
            if task_id in self._tasks:
                del self._tasks[task_id]
            if task_id in self._task_names:
                del self._task_names[task_id]
                
            # 保存结果
            self._completed_tasks.append(result)
            
        # 发送完成信号
        self.taskCompleted.emit(result)
        
        # 检查是否所有任务都完成了
        if not self._tasks:
            self.allTasksCompleted.emit(self._completed_tasks.copy())


# 单例模式
import threading
_task_manager_instance = None
_task_manager_lock = threading.Lock()


def get_task_manager() -> TaskManager:
    """获取任务管理器实例(线程安全的单例模式)
    
    Returns:
        TaskManager: 任务管理器实例
    """
    global _task_manager_instance
    if _task_manager_instance is None:
        with _task_manager_lock:
            if _task_manager_instance is None:
                _task_manager_instance = TaskManager()
    return _task_manager_instance
