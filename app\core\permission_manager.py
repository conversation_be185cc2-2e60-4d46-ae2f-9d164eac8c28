#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
权限管理模块
提供UAC权限检查和管理员权限提升功能
"""

import os
import sys
import ctypes
import subprocess
from typing import Optional, Tuple
from enum import Enum

from PySide6.QtCore import QObject, Signal
from PySide6.QtWidgets import QMessageBox

from app.core.exceptions import PermissionException, handle_exceptions


class PermissionLevel(Enum):
    """权限级别枚举"""
    USER = "user"           # 普通用户权限
    ADMIN = "admin"         # 管理员权限
    SYSTEM = "system"       # 系统权限


class PermissionManager(QObject):
    """权限管理器
    
    提供权限检查、权限提升和权限相关的操作管理
    """
    
    # 信号定义
    permissionChanged = Signal(str)  # 权限变更信号
    elevationRequested = Signal()    # 权限提升请求信号
    elevationFailed = Signal(str)    # 权限提升失败信号
    
    def __init__(self):
        super().__init__()
        self._current_permission = self._detect_current_permission()
        
    def _detect_current_permission(self) -> PermissionLevel:
        """检测当前权限级别
        
        Returns:
            PermissionLevel: 当前权限级别
        """
        try:
            if os.name == 'nt':  # Windows系统
                return PermissionLevel.ADMIN if self.is_admin() else PermissionLevel.USER
            else:
                # 非Windows系统
                return PermissionLevel.ADMIN if os.geteuid() == 0 else PermissionLevel.USER
        except Exception:
            return PermissionLevel.USER
            
    @handle_exceptions("权限检查")
    def is_admin(self) -> bool:
        """检查是否具有管理员权限
        
        Returns:
            bool: 是否为管理员权限
        """
        try:
            if os.name == 'nt':  # Windows系统
                return ctypes.windll.shell32.IsUserAnAdmin() != 0
            else:
                return os.geteuid() == 0
        except Exception as e:
            print(f"权限检查失败: {e}")
            return False
            
    @handle_exceptions("UAC检查")
    def is_uac_enabled(self) -> bool:
        """检查UAC是否启用
        
        Returns:
            bool: UAC是否启用
        """
        try:
            if os.name != 'nt':
                return False
                
            # 检查注册表中的UAC设置
            import winreg
            key = winreg.OpenKey(
                winreg.HKEY_LOCAL_MACHINE,
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"
            )
            
            try:
                value, _ = winreg.QueryValueEx(key, "EnableLUA")
                return value == 1
            except FileNotFoundError:
                return True  # 默认启用
            finally:
                winreg.CloseKey(key)
                
        except Exception as e:
            print(f"UAC检查失败: {e}")
            return True  # 默认假设启用
            
    @handle_exceptions("权限提升")
    def request_elevation(self, reason: str = "此操作需要管理员权限") -> bool:
        """请求权限提升
        
        Args:
            reason: 权限提升的原因说明
            
        Returns:
            bool: 是否成功获得管理员权限
        """
        if self.is_admin():
            return True
            
        try:
            # 发送权限提升请求信号
            self.elevationRequested.emit()
            
            if os.name == 'nt':
                return self._request_windows_elevation(reason)
            else:
                return self._request_unix_elevation(reason)
                
        except Exception as e:
            error_msg = f"权限提升失败: {e}"
            self.elevationFailed.emit(error_msg)
            raise PermissionException(error_msg)
            
    def _request_windows_elevation(self, reason: str) -> bool:
        """Windows系统权限提升
        
        Args:
            reason: 权限提升原因
            
        Returns:
            bool: 是否成功
        """
        try:
            # 使用ShellExecute请求管理员权限重启应用
            script_path = sys.argv[0]
            params = ' '.join(sys.argv[1:])
            
            result = ctypes.windll.shell32.ShellExecuteW(
                None,
                "runas",
                sys.executable,
                f'"{script_path}" {params}',
                None,
                1  # SW_SHOWNORMAL
            )
            
            # 如果成功启动了提升权限的进程，退出当前进程
            if result > 32:
                sys.exit(0)
            else:
                return False
                
        except Exception as e:
            print(f"Windows权限提升失败: {e}")
            return False
            
    def _request_unix_elevation(self, reason: str) -> bool:
        """Unix/Linux系统权限提升
        
        Args:
            reason: 权限提升原因
            
        Returns:
            bool: 是否成功
        """
        try:
            # 尝试使用sudo重启应用
            script_path = sys.argv[0]
            params = sys.argv[1:]
            
            cmd = ['sudo', sys.executable, script_path] + params
            result = subprocess.run(cmd, capture_output=True)
            
            if result.returncode == 0:
                sys.exit(0)
            else:
                return False
                
        except Exception as e:
            print(f"Unix权限提升失败: {e}")
            return False
            
    def check_permission_for_operation(self, operation: str, required_level: PermissionLevel = PermissionLevel.ADMIN) -> Tuple[bool, str]:
        """检查操作所需的权限
        
        Args:
            operation: 操作名称
            required_level: 所需权限级别
            
        Returns:
            Tuple[bool, str]: (是否有权限, 错误信息)
        """
        current_level = self._current_permission
        
        if required_level == PermissionLevel.ADMIN and current_level == PermissionLevel.USER:
            return False, f"操作 '{operation}' 需要管理员权限"
        elif required_level == PermissionLevel.SYSTEM:
            return False, f"操作 '{operation}' 需要系统级权限"
            
        return True, ""
        
    def get_current_permission(self) -> PermissionLevel:
        """获取当前权限级别
        
        Returns:
            PermissionLevel: 当前权限级别
        """
        return self._current_permission
        
    def refresh_permission_status(self):
        """刷新权限状态"""
        old_permission = self._current_permission
        self._current_permission = self._detect_current_permission()
        
        if old_permission != self._current_permission:
            self.permissionChanged.emit(self._current_permission.value)
            
    @handle_exceptions("权限验证")
    def verify_admin_access(self) -> bool:
        """验证管理员访问权限
        
        Returns:
            bool: 是否具有管理员权限
        """
        if not self.is_admin():
            raise PermissionException("当前用户没有管理员权限")
        return True
        
    def show_permission_dialog(self, parent=None, operation: str = "此操作") -> bool:
        """显示权限确认对话框
        
        Args:
            parent: 父窗口
            operation: 操作描述
            
        Returns:
            bool: 用户是否确认
        """
        try:
            from PySide6.QtWidgets import QMessageBox
            
            msg = QMessageBox(parent)
            msg.setWindowTitle("权限确认")
            msg.setIcon(QMessageBox.Icon.Warning)
            msg.setText(f"{operation}需要管理员权限")
            msg.setInformativeText("是否继续？这可能会弹出UAC提示。")
            msg.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            msg.setDefaultButton(QMessageBox.StandardButton.No)
            
            return msg.exec() == QMessageBox.StandardButton.Yes
            
        except Exception as e:
            print(f"显示权限对话框失败: {e}")
            return False


# 单例模式
import threading
_permission_manager_instance = None
_permission_manager_lock = threading.Lock()


def get_permission_manager() -> PermissionManager:
    """获取权限管理器实例(线程安全的单例模式)
    
    Returns:
        PermissionManager: 权限管理器实例
    """
    global _permission_manager_instance
    if _permission_manager_instance is None:
        with _permission_manager_lock:
            if _permission_manager_instance is None:
                _permission_manager_instance = PermissionManager()
    return _permission_manager_instance


def require_admin(operation: str = "此操作"):
    """管理员权限装饰器
    
    Args:
        operation: 操作描述
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            permission_manager = get_permission_manager()
            
            if not permission_manager.is_admin():
                raise PermissionException(f"{operation}需要管理员权限")
                
            return func(*args, **kwargs)
        return wrapper
    return decorator
