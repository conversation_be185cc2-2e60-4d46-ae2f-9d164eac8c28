#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主窗口模块
提供应用程序的主界面和导航功能，集成了所有功能视图模块
"""

from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon
from PySide6.QtWidgets import QWidget
from qfluentwidgets import (
    FluentWindow,
    NavigationItemPosition,
    FluentIcon as FIF,
    setTheme,
    Theme,
)

# 导入视图
from app.views import (
    HardwareInfoView,
    OptimizationView,
    AppRemovalView,
    OverclockingView,
    QuickToolsView,
    AboutView,
)


class MainWindow(FluentWindow):
    """主窗口

    继承自FluentWindow，提供应用程序的主界面框架和导航功能。
    整合了硬件信息、优化清理、预装应用管理、超频工具、快捷工具和关于软件等功能视图。
    """

    def __init__(self):
        """初始化主窗口

        设置窗口基本属性，实现视图懒加载，初始化导航栏，并设置应用主题
        """
        super().__init__()
        # 设置窗口基本属性
        self.setWindowTitle("Windows 硬件工具箱")
        self.setWindowIcon(QIcon("assets/icon.ico"))
        self.resize(900, 700)

        # 视图实例字典 - 懒加载
        self._views = {}
        self._view_classes = {
            "hardware_info": ("app.views", "HardwareInfoView"),
            "optimization": ("app.views", "OptimizationView"),
            "app_removal": ("app.views", "AppRemovalView"),
            "overclocking": ("app.views", "OverclockingView"),
            "quick_tools": ("app.views", "QuickToolsView"),
            "about": ("app.views", "AboutView"),
        }

        # 初始化导航栏
        self.initNavigation()

        # 设置主题
        setTheme(Theme.AUTO)  # 使用自动主题（根据系统设置自动切换亮色/暗色主题）

    def _get_or_create_view(self, view_key: str):
        """获取或创建视图实例（懒加载）

        Args:
            view_key: 视图键名

        Returns:
            QWidget: 视图实例
        """
        if view_key not in self._views:
            if view_key in self._view_classes:
                module_name, class_name = self._view_classes[view_key]
                try:
                    # 动态导入模块
                    module = __import__(module_name, fromlist=[class_name])
                    view_class = getattr(module, class_name)
                    # 创建视图实例
                    self._views[view_key] = view_class(self)
                except Exception as e:
                    print(f"创建视图 {view_key} 失败: {e}")
                    # 创建一个空的占位视图
                    from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel

                    placeholder = QWidget(self)
                    layout = QVBoxLayout(placeholder)
                    layout.addWidget(QLabel(f"视图加载失败: {view_key}"))
                    self._views[view_key] = placeholder

        return self._views[view_key]

    def initNavigation(self):
        """初始化导航栏

        添加顶部导航项：硬件信息、优化清理、预装应用、超频工具、快捷工具
        添加底部导航项：关于软件
        设置导航栏宽度和默认展开状态
        """
        # 添加导航项，使用懒加载的视图
        self.addSubInterface(
            self._get_or_create_view("hardware_info"),
            FIF.IOT,
            "硬件信息",
            NavigationItemPosition.TOP,
        )

        self.addSubInterface(
            self._get_or_create_view("optimization"),
            FIF.BRUSH,
            "优化清理",
            NavigationItemPosition.TOP,
        )

        self.addSubInterface(
            self._get_or_create_view("app_removal"),
            FIF.APPLICATION,
            "预装应用",
            NavigationItemPosition.TOP,
        )

        self.addSubInterface(
            self._get_or_create_view("overclocking"),
            FIF.SPEED_HIGH,
            "超频工具",
            NavigationItemPosition.TOP,
        )

        self.addSubInterface(
            self._get_or_create_view("quick_tools"),
            FIF.TILES,
            "快捷工具",
            NavigationItemPosition.TOP,
        )

        # 在底部添加"关于软件"导航项
        self.addSubInterface(
            self._get_or_create_view("about"),
            FIF.INFO,
            "关于软件",
            NavigationItemPosition.BOTTOM,
        )

        # 设置导航栏宽度
        self.navigationInterface.setExpandWidth(200)

        # 默认展开导航栏
        self.navigationInterface.setMinimumExpandWidth(900)
