#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主窗口模块
提供应用程序的主界面和导航功能，集成了所有功能视图模块
"""

from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon
from PySide6.QtWidgets import QWidget
from qfluentwidgets import (
    FluentWindow,
    NavigationItemPosition,
    FluentIcon as FIF,
    setTheme,
    Theme,
)

# 导入视图
from app.views import (
    HardwareInfoView,
    OptimizationView,
    AppRemovalView,
    OverclockingView,
    QuickToolsView,
    AboutView,
)


class MainWindow(FluentWindow):
    """主窗口

    继承自FluentWindow，提供应用程序的主界面框架和导航功能。
    整合了硬件信息、优化清理、预装应用管理、超频工具、快捷工具和关于软件等功能视图。
    """

    def __init__(self):
        """初始化主窗口

        设置窗口基本属性，创建视图实例，初始化导航栏，并设置应用主题
        """
        super().__init__()
        # 设置窗口基本属性
        self.setWindowTitle("Windows 硬件工具箱")
        self.setWindowIcon(QIcon("assets/icon.ico"))
        self.resize(900, 700)

        # 创建视图实例
        self.hardwareInfoView = HardwareInfoView(self)
        self.optimizationView = OptimizationView(self)
        self.appRemovalView = AppRemovalView(self)
        self.overclockingView = OverclockingView(self)
        self.quickToolsView = QuickToolsView(self)
        self.aboutView = AboutView(self)

        # 初始化导航栏
        self.initNavigation()

        # 设置主题
        setTheme(Theme.AUTO)  # 使用自动主题（根据系统设置自动切换亮色/暗色主题）

    def initNavigation(self):
        """初始化导航栏

        添加顶部导航项：硬件信息、优化清理、预装应用、超频工具、快捷工具
        添加底部导航项：关于软件
        设置导航栏宽度和默认展开状态
        """
        # 添加导航项，链接到对应的视图
        self.addSubInterface(
            self.hardwareInfoView, FIF.IOT, "硬件信息", NavigationItemPosition.TOP
        )

        self.addSubInterface(
            self.optimizationView, FIF.BRUSH, "优化清理", NavigationItemPosition.TOP
        )

        self.addSubInterface(
            self.appRemovalView, FIF.APPLICATION, "预装应用", NavigationItemPosition.TOP
        )

        self.addSubInterface(
            self.overclockingView,
            FIF.SPEED_HIGH,
            "超频工具",
            NavigationItemPosition.TOP,
        )

        self.addSubInterface(
            self.quickToolsView, FIF.TILES, "快捷工具", NavigationItemPosition.TOP
        )

        # 在底部添加"关于软件"导航项
        self.addSubInterface(
            self.aboutView, FIF.INFO, "关于软件", NavigationItemPosition.BOTTOM
        )

        # 设置导航栏宽度
        self.navigationInterface.setExpandWidth(200)

        # 默认展开导航栏
        self.navigationInterface.setMinimumExpandWidth(900)
