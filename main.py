#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import traceback

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt, QLocale
from qfluentwidgets import FluentTranslator

# 导入硬件管理器
from app.core import get_hardware_manager

# 导入主窗口
from app.main_window import MainWindow


if __name__ == "__main__":
    try:
        # 创建应用实例
        app = QApplication(sys.argv)

        # 安装翻译器
        translator = FluentTranslator(QLocale(QLocale.Chinese, QLocale.China))
        app.installTranslator(translator)

        # 禁用native siblings的属性，避免与Qt的frameless window产生冲突
        app.setAttribute(Qt.ApplicationAttribute.AA_DontCreateNativeWidgetSiblings)

        # 在创建主窗口之前获取硬件信息
        hardware_manager = get_hardware_manager()
        hardware_manager.fetch_hardware_info_sync()

        # 创建并显示主窗口
        window = MainWindow()
        window.show()

        # 运行应用事件循环
        sys.exit(app.exec())
    except Exception as e:
        print(f"程序出现异常: {e}")
        traceback.print_exc()
